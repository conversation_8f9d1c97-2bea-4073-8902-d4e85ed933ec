<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Fix enum values for trial_duration_type and billing_cycle_type
        DB::statement("ALTER TABLE products MODIFY COLUMN trial_duration_type ENUM('days', 'months', 'years') DEFAULT 'days'");
        DB::statement("ALTER TABLE products MODIFY COLUMN billing_cycle_type ENUM('monthly', 'yearly', 'weekly') DEFAULT 'monthly'");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Revert to original enum values
        DB::statement("ALTER TABLE products MODIFY COLUMN trial_duration_type ENUM('day', 'month', 'year') DEFAULT 'day'");
        DB::statement("ALTER TABLE products MODIFY COLUMN billing_cycle_type ENUM('forever', 'limited') DEFAULT 'forever'");
    }
};
