<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('nickname')->nullable();
            $table->boolean('is_one_time_only')->default(false);
            $table->boolean('is_free_trial')->default(false);
            $table->enum('trial_duration_type', ['days', 'months', 'years'])->default('days');
            $table->integer('trial_duration')->default(0);
            $table->decimal('total_trial_price', 10, 2)->default(0.00);
            $table->boolean('is_subscription')->default(false);
            $table->boolean('is_cancelable')->default(true);
            $table->integer('billing_every')->default(1); // 1 month/year/etc.
            $table->enum('billing_cycle_type', ['monthly', 'yearly', 'weekly'])->default('monthly');
            $table->integer('billing_cycle_limit')->nullable();
            $table->decimal('product_price', 10, 2);
            $table->decimal('striked_price', 10, 2)->nullable();
            $table->decimal('downpayment', 10, 2)->default(0.00);
            $table->boolean('show_shipping_field')->default(false);
            $table->boolean('require_shipping_field')->default(false);
            $table->string('payment_gateway')->nullable();
            $table->string('product_image')->nullable();
            $table->boolean('skip_gst_form')->default(false);
            $table->string('hsn_sac_no', 50)->nullable();
            $table->string('redirect_url')->nullable();
            $table->text('product_description')->nullable();
            $table->text('invoice_footer_description')->nullable();
            $table->foreignId('tax_slab_id')->nullable()->constrained('tax_slabs')->onDelete('set null');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
